# This is just an example, resumatter doesn't load .env file
# But could be useful for docker quick start:
# docker run --rm -d --name resumatter-demo --env-file .env ghcr.io/ajiwo/resumatter:latest
#
# Google AI API Key (required)
# Primary: Use RESUMATTER_AI_APIKEY (recommended)
RESUMATTER_AI_APIKEY=your_google_ai_api_key_here
# Fallback: GEMINI_API_KEY (for backward compatibility)
# GEMINI_API_KEY=your_google_ai_api_key_here

# AI Configuration
RESUMATTER_AI_PROVIDER=gemini
RESUMATTER_AI_MODEL=gemini-1.5-flash
RESUMATTER_AI_TIMEOUT=60s
RESUMATTER_AI_MAXRETRIES=3
RESUMATTER_AI_TEMPERATURE=0.7

# System Prompt Configuration
# Set to true to use separate system and user prompts (recommended)
# Set to false to use legacy combined prompts for backward compatibility
RESUMATTER_AI_USESYSTEMPROMPTS=true

# Server Configuration (optional - defaults shown)
# RESUMATTER_SERVER_HOST=localhost
# RESUMATTER_SERVER_PORT=8080

# TLS Configuration (new structure)
# RESUMATTER_SERVER_TLS_MODE=disabled              # disabled, server, mutual
# RESUMATTER_SERVER_TLS_CERTFILE=/path/to/server.crt
# RESUMATTER_SERVER_TLS_KEYFILE=/path/to/server.key
# RESUMATTER_SERVER_TLS_CAFILE=/path/to/ca.crt     # Required for mutual mode
# RESUMATTER_SERVER_TLS_MINVERSION=1.2             # 1.2 or 1.3
# RESUMATTER_SERVER_TLS_CLIENTAUTHPOLICY=require   # require, request, verify
# RESUMATTER_SERVER_TLS_INSECURESKIPVERIFY=false   # Dev only - DO NOT USE IN PROD

# TLS Auto-Reload Configuration (zero-downtime certificate updates)
# RESUMATTER_SERVER_TLS_AUTORELOAD_ENABLED=true
# RESUMATTER_SERVER_TLS_AUTORELOAD_CHECKINTERVAL=30s
# RESUMATTER_SERVER_TLS_AUTORELOAD_PREEMPTIVERENEWAL=72h
# RESUMATTER_SERVER_TLS_AUTORELOAD_MAXRETRIES=3
# RESUMATTER_SERVER_TLS_AUTORELOAD_RETRYDELAY=10s

# File Watcher Configuration (monitors certificate files for changes)
# RESUMATTER_SERVER_TLS_AUTORELOAD_FILEWATCHER_ENABLED=true
# RESUMATTER_SERVER_TLS_AUTORELOAD_FILEWATCHER_DEBOUNCEDELAY=1s

# Vault Watcher Configuration (for Vault-based certificate management)
# RESUMATTER_SERVER_TLS_AUTORELOAD_VAULTWATCHER_ENABLED=false
# RESUMATTER_SERVER_TLS_AUTORELOAD_VAULTWATCHER_POLLINTERVAL=5m
# RESUMATTER_SERVER_TLS_AUTORELOAD_VAULTWATCHER_AUTORENEW=true
# RESUMATTER_SERVER_TLS_AUTORELOAD_VAULTWATCHER_RENEWTHRESHOLD=24h
# RESUMATTER_SERVER_TLS_AUTORELOAD_VAULTWATCHER_SECRETPATH=secret/data/resumatter/tls


# API Authentication (comma-separated list of valid API keys)
# RESUMATTER_SERVER_APIKEYS=sk-resumatter-prod-abc123,sk-resumatter-dev-xyz789

# App Configuration (optional - defaults shown)
# RESUMATTER_APP_LOGLEVEL=info
# RESUMATTER_APP_DEFAULTFORMAT=json

# Vault Configuration (optional but recommended)
# RESUMATTER_VAULT_ENABLED=true
# RESUMATTER_VAULT_ADDRESS=https://vault.company.com:8200
# RESUMATTER_VAULT_TOKEN=hvs.your-vault-token-here
# RESUMATTER_VAULT_TOKENFILE=/var/secrets/vault-token
# RESUMATTER_VAULT_NAMESPACE=resumatter
# RESUMATTER_VAULT_SECRETS_APIKEYS=secret/data/resumatter/api-keys
# RESUMATTER_VAULT_SECRETS_GEMINIKEY=secret/data/resumatter/gemini
# RESUMATTER_VAULT_SECRETS_TLSCERTS=secret/data/resumatter/tls
#
# When using Vault for TLS certificates, store the actual certificate content:
# vault kv put secret/resumatter/tls \
#   cert="$(cat server.crt)" \
#   key="$(cat server.key)" \
#   ca="$(cat ca.crt)"
#
# Note: File path fields (cert_file, key_file, ca_file) are no longer supported in Vault.
#
# Auto-reload integration with Vault:
# When Vault is enabled for TLS certificates, the Vault watcher will automatically
# monitor the secret for changes and reload certificates without server restart.
# This works seamlessly with Vault's dynamic secrets and lease renewal.