# Resumatter Prometheus Integration Analysis

## Overview

Resumatter implements a **dedicated Prometheus metrics server** that runs separately from the main HTTP API server, providing comprehensive observability for AI operations, business metrics, and infrastructure monitoring.

## Architecture

### Dual Server Setup
```
┌─────────────────┐    ┌──────────────────────┐
│   Main Server   │    │  Prometheus Server   │
│   Port: 8080    │    │    Port: 9090        │
│                 │    │                      │
│ • /health       │    │ • /metrics           │
│ • /tailor       │    │   (OpenTelemetry     │
│ • /evaluate     │    │    exported metrics) │
│ • /analyze      │    │                      │
└─────────────────┘    └──────────────────────┘
```

### Implementation Details

#### 1. **Prometheus Server Initialization**
```go
// From internal/observability/otel.go
if om.config.Prometheus.Enabled {
    prometheusReader, prometheusMux, err := SetupPrometheusExporter(om.config.Prometheus)
    if prometheusReader != nil {
        readers = append(readers, prometheusReader)
        om.prometheusServer = prometheusMux
        
        // Start dedicated Prometheus server
        StartPrometheusServer(prometheusMux, om.config.Prometheus.Port)
    }
}
```

#### 2. **Prometheus Exporter Setup**
```go
// From internal/observability/prometheus.go
func SetupPrometheusExporter(config PrometheusConfig) (metric.Reader, *http.ServeMux, error) {
    // Create OpenTelemetry Prometheus exporter
    exporter, err := prometheus.New()
    
    // Create HTTP handler for metrics endpoint
    mux := http.NewServeMux()
    mux.Handle(config.Endpoint, promhttp.Handler()) // Default: "/metrics"
    
    return exporter, mux, nil
}
```

#### 3. **Dedicated Server Launch**
```go
func StartPrometheusServer(mux *http.ServeMux, port string) error {
    server := &http.Server{
        Addr:              ":" + port,
        Handler:           mux,
        ReadHeaderTimeout: 10 * time.Second,
        ReadTimeout:       30 * time.Second,
        WriteTimeout:      30 * time.Second,
        IdleTimeout:       60 * time.Second,
    }
    
    // Start server in background goroutine
    go server.ListenAndServe()
}
```

## Configuration

### Default Configuration
```yaml
observability:
  prometheus:
    enabled: true
    endpoint: "/metrics"
    port: "9090"
```

### Environment Variables
```bash
export RESUMATTER_OBSERVABILITY_PROMETHEUS_ENABLED=true
export RESUMATTER_OBSERVABILITY_PROMETHEUS_PORT=9090
export RESUMATTER_OBSERVABILITY_PROMETHEUS_ENDPOINT="/metrics"
```

### Configuration Precedence
1. **Environment variables** (highest priority)
2. **Config file values**
3. **Default values** (lowest priority)

## Available Metrics

### AI Operation Metrics
```prometheus
# AI processing performance
resumatter_ai_processing_duration_seconds{operation="tailor",success="true"}

# Request counting
resumatter_ai_requests_total{operation="analyze",success="true"}

# Error tracking
resumatter_ai_errors_total{operation="evaluate",success="false"}

# Token usage (cost tracking)
resumatter_ai_token_usage_total{operation="tailor",token_type="input"}
resumatter_ai_token_usage_total{operation="tailor",token_type="output"}
resumatter_ai_token_usage_total{operation="tailor",token_type="total"}
```

### Business Metrics
```prometheus
# Core business operations
resumatter_resumes_tailored_total{success="true"}
resumatter_jobs_analyzed_total{success="true"}
resumatter_resumes_evaluated_total{success="true"}
```

### Infrastructure Metrics
```prometheus
# Certificate management
resumatter_cert_reloads_total{success="true"}
resumatter_cert_expiry_seconds

# Rate limiting
resumatter_rate_limit_hits_total{endpoint="/tailor",method="POST"}
```

### Rich Attributes
Each metric includes detailed labels for powerful querying:
- **operation**: tailor, evaluate, analyze
- **success**: true/false
- **token_type**: input, output, total
- **endpoint**: API endpoint path
- **method**: HTTP method
- **error**: error type/message

## Integration Features

### 1. **OpenTelemetry Native**
- Uses OpenTelemetry Prometheus exporter
- Automatic metric registration
- Standard Prometheus format output

### 2. **Concurrent Operation**
- Prometheus server runs independently
- No impact on main API performance
- Separate port for metrics collection

### 3. **Security Considerations**
- Metrics server typically internal-only
- No authentication on metrics endpoint
- Firewall rules should restrict access

### 4. **High Availability**
- Metrics server starts automatically
- Graceful error handling
- Continues operation if metrics fail

## Usage Examples

### 1. **Manual Metrics Check**
```bash
# Check if Prometheus server is running
curl http://localhost:9090/metrics

# Filter for Resumatter metrics
curl -s http://localhost:9090/metrics | grep resumatter_

# Check specific metric
curl -s http://localhost:9090/metrics | grep resumatter_ai_requests_total
```

### 2. **Prometheus Server Configuration**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'resumatter'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 10s
    metrics_path: '/metrics'
```

### 3. **Docker Compose Integration**
```yaml
version: '3.8'
services:
  resumatter:
    image: resumatter:latest
    ports:
      - "8080:8080"  # API server
      - "9090:9090"  # Prometheus metrics
    environment:
      - RESUMATTER_OBSERVABILITY_PROMETHEUS_ENABLED=true
      
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"  # Prometheus UI
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--web.enable-lifecycle'
```

## Monitoring Queries

### Performance Monitoring
```promql
# AI Request Rate
rate(resumatter_ai_requests_total[5m])

# Success Rate
rate(resumatter_ai_requests_total{success="true"}[5m]) / 
rate(resumatter_ai_requests_total[5m]) * 100

# P95 Latency
histogram_quantile(0.95, 
  rate(resumatter_ai_processing_duration_seconds_bucket[5m]))

# Error Rate
rate(resumatter_ai_errors_total[5m]) / 
rate(resumatter_ai_requests_total[5m]) * 100
```

### Business Metrics
```promql
# Resume Tailoring Volume
rate(resumatter_resumes_tailored_total[1h])

# Job Analysis Success Rate
rate(resumatter_jobs_analyzed_total{success="true"}[5m])

# Daily Operations Count
increase(resumatter_resumes_tailored_total[24h])
```

### Cost Tracking
```promql
# Token Usage Rate
rate(resumatter_ai_token_usage_total[5m])

# Input vs Output Token Ratio
rate(resumatter_ai_token_usage_total{token_type="output"}[5m]) /
rate(resumatter_ai_token_usage_total{token_type="input"}[5m])

# Total Token Consumption
sum(rate(resumatter_ai_token_usage_total{token_type="total"}[5m]))
```

### Infrastructure Monitoring
```promql
# Certificate Expiry Alert
resumatter_cert_expiry_seconds < 86400  # Less than 24 hours

# Rate Limit Hit Rate
rate(resumatter_rate_limit_hits_total[5m])

# Certificate Reload Failures
rate(resumatter_cert_reloads_total{success="false"}[5m])
```

## Alerting Rules

### Critical Alerts
```yaml
groups:
  - name: resumatter.critical
    rules:
      - alert: ResumeatterDown
        expr: up{job="resumatter"} == 0
        for: 1m
        
      - alert: HighErrorRate
        expr: rate(resumatter_ai_errors_total[5m]) / rate(resumatter_ai_requests_total[5m]) > 0.1
        for: 2m
        
      - alert: CertificateExpiringSoon
        expr: resumatter_cert_expiry_seconds < 86400
        for: 0m
```

### Warning Alerts
```yaml
  - name: resumatter.warning
    rules:
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(resumatter_ai_processing_duration_seconds_bucket[5m])) > 30
        for: 5m
        
      - alert: RateLimitHits
        expr: rate(resumatter_rate_limit_hits_total[5m]) > 0
        for: 1m
```

## Troubleshooting

### Common Issues

#### 1. **Metrics Server Not Starting**
```bash
# Check configuration
grep -A5 prometheus config.yaml

# Check logs for errors
./resumatter serve 2>&1 | grep -i prometheus

# Verify port availability
netstat -ln | grep 9090
```

#### 2. **No Metrics Appearing**
```bash
# Check if metrics are being generated
curl -s http://localhost:9090/metrics | wc -l

# Verify OpenTelemetry integration
curl -s http://localhost:9090/metrics | grep otel_

# Check metric collection interval
grep collectionInterval config.yaml
```

#### 3. **Port Conflicts**
```yaml
# Change Prometheus port
observability:
  prometheus:
    enabled: true
    port: "9091"  # Use different port
```

## Best Practices

### 1. **Production Deployment**
- Run Prometheus server on internal network only
- Use service discovery for dynamic targets
- Configure appropriate retention policies
- Set up alerting for critical metrics

### 2. **Performance Optimization**
```yaml
observability:
  metrics:
    collectionInterval: "30s"  # Reduce frequency for high-volume
  prometheus:
    enabled: true
    port: "9090"
```

### 3. **Security**
- Firewall metrics port from external access
- Use network policies in Kubernetes
- Monitor metrics endpoint access
- Consider authentication proxy if needed

### 4. **Monitoring Strategy**
- Set up dashboards for key metrics
- Configure alerting for SLA violations
- Monitor both technical and business metrics
- Track cost metrics for AI usage

## Integration Status

**Resumatter's Prometheus integration is:**
- **Fully implemented** with dedicated server
- **Production-ready** with proper error handling
- **Comprehensive** covering AI, business, and infrastructure metrics
- **Standards-compliant** using OpenTelemetry exporters
- **Configurable** with environment variables and config files
- **Scalable** with separate server architecture

The implementation follows Prometheus best practices and provides enterprise-grade observability for AI-powered resume processing operations.