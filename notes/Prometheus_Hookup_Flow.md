# Prometheus Hookup Flow in Resumatter

## Complete Initialization Chain

Here's exactly where and how Prometheus gets hooked into Resumatter's startup process:

### 1. **Application Entry Point**
```go
// cmd/resumatter/main.go:40
func main() {
    // Execute command with cancellable context
    if err := cli.Execute(ctx, cfg, logger); err != nil {
        logger.LogError(err, "Application execution failed")
        os.Exit(1)
    }
}
```

### 2. **CLI Command Routing**
```go
// internal/cli/serve.go:75
func runServe(cmd *cobra.Command, args []string) error {
    cfg := getConfigFromContext(cmd.Context())
    logger := getLoggerFromContext(cmd.Context())
    
    // Create server configuration
    serverCfg := server.ServerConfig{...}
    
    // KEY HOOKUP POINT: Start server with full config
    return server.NewServer(cfg, serverCfg, logger).Start()
}
```

### 3. **Server Initialization**
```go
// internal/server/http.go:132-150
func (s *Server) Start() error {
    // PROMETHEUS HOOKUP STARTS HERE
    // Initialize observability with Prometheus config
    obsConfig := observability.ObservabilityConfig{
        ServiceName:    s.AppConfig.Observability.ServiceName,
        ServiceVersion: s.Version,
        Enabled:        s.AppConfig.Observability.Enabled,
        ConsoleOutput:  s.AppConfig.Observability.ConsoleOutput,
        PrettyPrint:    s.AppConfig.Observability.Console.PrettyPrint,
        SampleRate:     s.AppConfig.Observability.SampleRate,
        Prometheus: observability.PrometheusConfig{
            Enabled:  s.AppConfig.Observability.Prometheus.Enabled,  // Config read
            Endpoint: s.AppConfig.Observability.Prometheus.Endpoint, // "/metrics"
            Port:     s.AppConfig.Observability.Prometheus.Port,     // "9090"
        },
    }
    
    // CREATE OBSERVABILITY MANAGER (Prometheus starts here)
    om, err := observability.NewObservabilityManager(obsConfig, s.AppConfig)
    if err != nil {
        return fmt.Errorf("failed to initialize observability: %w", err)
    }
    
    // ... rest of server setup
}
```

### 4. **Observability Manager Creation**
```go
// internal/observability/otel.go:71-95
func NewObservabilityManager(obsConfig ObservabilityConfig, fullConfig *config.Config) (*ObservabilityManager, error) {
    if !obsConfig.Enabled {
        return &ObservabilityManager{config: obsConfig, fullConfig: fullConfig}, nil
    }

    om := &ObservabilityManager{
        config:        obsConfig,
        fullConfig:    fullConfig,
        shutdownFuncs: make([]func(context.Context) error, 0),
    }

    if err := om.initResource(); err != nil {
        return nil, fmt.Errorf("failed to initialize resource: %w", err)
    }

    if err := om.initTracing(); err != nil {
        return nil, fmt.Errorf("failed to initialize tracing: %w", err)
    }

    // PROMETHEUS GETS INITIALIZED HERE
    if err := om.initMetrics(); err != nil {
        return nil, fmt.Errorf("failed to initialize metrics: %w", err)
    }

    return om, nil
}
```

### 5. **Metrics Initialization (Prometheus Hookup)**
```go
// internal/observability/otel.go:170-212
func (om *ObservabilityManager) initMetrics() error {
    var readers []sdkmetric.Reader
    var err error

    // Console exporter for development
    if om.config.ConsoleOutput {
        exporter, err := stdoutmetric.New()
        // ... setup console reader
    }

    // OTLP exporter for production metrics
    if om.fullConfig != nil && om.fullConfig.Observability.OTLP.Enabled {
        // ... setup OTLP reader
    }

    // PROMETHEUS EXPORTER SETUP - THE ACTUAL HOOKUP!
    if om.config.Prometheus.Enabled {
        prometheusReader, prometheusMux, err := SetupPrometheusExporter(om.config.Prometheus)
        if err != nil {
            return fmt.Errorf("failed to create Prometheus exporter: %w", err)
        }
        if prometheusReader != nil {
            readers = append(readers, prometheusReader)
            om.prometheusServer = prometheusMux

            // START DEDICATED PROMETHEUS SERVER
            if err := StartPrometheusServer(prometheusMux, om.config.Prometheus.Port); err != nil {
                return fmt.Errorf("failed to start Prometheus server: %w", err)
            }
        }
    }

    // Create meter provider with all readers (including Prometheus)
    mp := sdkmetric.NewMeterProvider(meterProviderOptions...)
    otel.SetMeterProvider(mp)
    om.meterProvider = mp

    // Initialize custom metrics
    return om.initCustomMetrics()
}
```

### 6. **Prometheus Exporter Setup**
```go
// internal/observability/prometheus.go:21-40
func SetupPrometheusExporter(config PrometheusConfig) (metric.Reader, *http.ServeMux, error) {
    if !config.Enabled {
        return nil, nil, nil
    }

    // Create OpenTelemetry Prometheus exporter
    exporter, err := prometheus.New()
    if err != nil {
        return nil, nil, fmt.Errorf("failed to create Prometheus exporter: %w", err)
    }

    // Create HTTP handler for metrics endpoint
    mux := http.NewServeMux()
    // REGISTER /metrics ENDPOINT
    mux.Handle(config.Endpoint, promhttp.Handler()) // Default: "/metrics"

    return exporter, mux, nil
}
```

### 7. **Prometheus Server Launch**
```go
// internal/observability/prometheus.go:42-69
func StartPrometheusServer(mux *http.ServeMux, port string) error {
    if mux == nil {
        return nil // No Prometheus server to start
    }

    addr := ":" + port
    fmt.Printf("Starting Prometheus metrics server on http://localhost%s\n", addr)
    fmt.Printf("Metrics available at: http://localhost%s/metrics\n", addr)

    server := &http.Server{
        Addr:              addr,                    // ":9090"
        Handler:           mux,                     // Metrics handler
        ReadHeaderTimeout: 10 * time.Second,
        ReadTimeout:       30 * time.Second,
        WriteTimeout:      30 * time.Second,
        IdleTimeout:       60 * time.Second,
    }

    // START SERVER IN BACKGROUND GOROUTINE
    go func() {
        if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            fmt.Printf("Prometheus server error: %v\n", err)
        }
    }()

    return nil
}
```

## Complete Flow Summary

```
main() 
  ↓
cli.Execute() 
  ↓
runServe() 
  ↓
server.NewServer().Start() 
  ↓
observability.NewObservabilityManager() 
  ↓
om.initMetrics() 
  ↓
SetupPrometheusExporter() 
  ↓
StartPrometheusServer() 
  ↓
Prometheus server running on :9090/metrics
```

## Key Hookup Points

1. **Configuration Reading**: `internal/server/http.go:141-145`
   - Reads Prometheus config from application config
   - Passes to ObservabilityManager

2. **Exporter Creation**: `internal/observability/otel.go:198-211`
   - Creates OpenTelemetry Prometheus exporter
   - Sets up HTTP multiplexer with /metrics endpoint

3. **Server Launch**: `internal/observability/prometheus.go:42-69`
   - Starts dedicated HTTP server on port 9090
   - Runs in background goroutine
   - Independent of main API server

4. **Metrics Registration**: `internal/observability/otel.go:250-347`
   - Registers all custom Resumatter metrics
   - AI operation metrics, business metrics, infrastructure metrics

## Configuration Sources

The Prometheus configuration comes from:

1. **Default values** (internal/config/config.go:487-490):
   ```go
   v.SetDefault("observability.prometheus.enabled", true)
   v.SetDefault("observability.prometheus.endpoint", "/metrics")
   v.SetDefault("observability.prometheus.port", "9090")
   ```

2. **Config file** (config.yaml):
   ```yaml
   observability:
     prometheus:
       enabled: true
       endpoint: "/metrics"
       port: "9090"
   ```

3. **Environment variables**:
   ```bash
   RESUMATTER_OBSERVABILITY_PROMETHEUS_ENABLED=true
   RESUMATTER_OBSERVABILITY_PROMETHEUS_PORT=9090
   ```

## Result

When Resumatter starts:
1. Main API server runs on port 8080
2. Prometheus metrics server runs on port 9090
3. Metrics are available at `http://localhost:9090/metrics`
4. Both servers run concurrently and independently